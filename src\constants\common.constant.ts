import { DataAreaSettingType } from "@/features/area-setting/type";
import { IMerchantCoreType } from "@/features/auth";

// Import payment method images
import group1261 from "/images/group_1261.svg"; // PayPay
import group1263 from "/images/group_1263.svg"; // d払い
import group1265 from "/images/group_1265.svg"; // auPAY
import group1268 from "/images/group_1268.svg"; // メルペイ
import group1270 from "/images/group_1270.svg"; // WeChatPay
import group1272 from "/images/group_1272.svg"; // アリペイ
import group1276 from "/images/group_1276.png"; // BankPay
import group1278 from "/images/group_1278.svg"; // threedot1dot1
import group1280 from "/images/group_1280.svg"; // threedot1dot2
import group1282 from "/images/group_1282.svg"; // threedot1dot3
import group1284 from "/images/group_1284.svg"; // threedot1dot4
import group1286 from "/images/group_1286.svg"; // threedot1dot5
import group1288 from "/images/group_1288.svg"; // threedot2dot1
import group1292 from "/images/group_1292.svg"; // threedot2dot2
import group1294 from "/images/group_1294.svg"; // threedot2dot3
import group1296 from "/images/group_1296.svg"; // threedot2dot4
import group1299 from "/images/group_1299.svg"; // WAON
import group1300 from "/images/group_1300.svg"; // 銀聯
import group1301 from "/images/group_1301.svg"; // iD
import group1308 from "/images/group_1308.svg"; // nanaco
import group1310 from "/images/group_1310.svg"; // Edy
import group1312 from "/images/group_1312.svg"; // QUICPay

// Export images for use throughout the application
export {
  group1261,
  group1263,
  group1265,
  group1268,
  group1270,
  group1272,
  group1276,
  group1278,
  group1280,
  group1282,
  group1284,
  group1286,
  group1288,
  group1292,
  group1294,
  group1296,
  group1299,
  group1300,
  group1301,
  group1308,
  group1310,
  group1312,
};

export const SITEKEY = '6LdKuUYhAAAAAGzSRvwNjE-LAKAaRkcP6Hyo8Pmc';

export const mapAccountType = new Map([[*********, "普通"], [*********, "当座"]]);
export const mapBusinessType = new Map([[*********, "クリニック"], [*********, "薬局"], [*********, "歯科"], [*********, "病院（20-199床）"], [*********, "病院（200床以上）"], [*********, "薬局（年商100億円以上）"], [*********, "B2B"], [*********, "クリニック(海外)"], [*********, "薬局（中小企業）"]]);
export const mapBusinessForm = new Map([[*********, "法人"], [*********, "個人"]]);


export const mapTransactionType = new Map([
    [*********, "クレジットカード一括"]
    , [*********, "クレジットカードボーナス"]
    , [*********, "クレジットカード分割"]
    , [*********, "クレジットカードリボ"]
    , [*********, "銀聯"]
    , [*********, "交通系電子マネー"]
    , [*********, "WAON"]
    , [*********, "nanaco"]
    , [*********, "Edy"]
    , [*********, "iD"]
    , [*********, "PayPay"]
    , [*********, "メルペイ"]
    , [*********, "auPAY"]
    , [*********, "LinePay"]
    , [*********, "d払い"]
    , [*********, "アリペイ"]
    , [*********, "WeChatPay"]
    , [*********, "BankPay"]
    , [*********, "クレジットカード一括(JCB)"]
    , [*********, "クレジットカード2回払(JCB)"]
    , [*********, "クレジットカードボーナス(JCB)"]
    , [*********, "クレジットカード分割(JCB)"]
    , [*********, "クレジットカードリボ(JCB)"]
    , [*********, "JCBプレモ"]
    , [*********, "QUICPay"]
]);

// Enum for status codes
export enum OtherMeasuresStatus {
    IMPLEMENTED = *********,
    NOT_PLANNED = *********,
    PLANNED = *********,
}

export const STEP = {
    HOUJIN: 1,
    DAIHYOUSHA: 2,
    SHOPINFO: 3,
    BANK: 4,
    ADDITIONAL: 5,
    ADDITIONAL1: 6,
    CHOQIPAY: 7,
    KIYAKU: 8,
    CONFIRM: 9,
    COMPLETE: 10,
} as const;

export enum MAIL_REGISTER {
    SEND_MAIL = 1,
    SEND_MAIL_SUCCESS = 2,
}

export enum REGISTER {
    PASSWORD = 1,
    BUSINESS_TYPE = 2,
    ENTITY_TYPE = 3,
    MEDICAL_CODE = 4,
    REFERRAL_CODE = 5,
    CONFIRMATION = 6,
    SUCCESS = 7,
}

export enum ERROR_CODE {
    TOKEN_EXPIRED = 43,
    ACCOUNT_ALREADY_EXISTS = 47,
    INVALID_REQUEST = 417,
    SERVER_ERROR = 500
}

// Admin transaction type mapping (string keys)
export const adminMapTransactionType = new Map([
    ['1', 'クレジット'],
    ['2', 'デビット'],
    ['3', 'プリペイド'],
    ['4', 'QRコード'],
    ['5', 'その他']
]);

export const merchantCore:IMerchantCoreType  = {
    agxMerchantNo: null,
    agxMerchantid: '',
    agxContactId: '',
    agxBusinessType: *********,
    agxBusinessForm: *********,
    agxMedicalInstitutionCode: '',
    agxEmsEmployeeNo: '',
    agxCorporateName: '',
    agxCorporatePhoneticName: '',
    agxCorporateEnglishName: '',
    agxCorporateNumber: '',
    agxCorporatePostalCode: '',
    agxCorporatePrefecture: '', //北海道
    agxCorporateAddress1: '',
    agxCorporateAddress2: '',
    agxCorporatePhoneticPrefecture: '',
    agxCorporatePhoneticAddress1: '',
    agxCorporatePhoneticAddress2: '',
    agxCorporatePhoneNumber: '',
    agxCorporateFaxNumber: '',
    agxRepresentativeName: '',
    agxRepresentativePhoneticName: '',
    agxRepresentativeGender: *********,
    agxRepresentativeBirthday: '',
    agxRepresentativeAddressCopyFlag: false,
    agxRepresentativePostalCode: '',
    agxRepresentativePrefecture: '', //北海道
    agxRepresentativeAddress1: '',
    agxRepresentativeAddress2: '',
    agxRepresentativePhoneticPrefecture: '',
    agxRepresentativePhoneticAddress1: '',
    agxRepresentativePhoneticAddress2: '',
    agxRepresentativePhoneNumber: '',
    agxRepresentativeFaxNumber: '',
    agxStoreName: '',
    agxStorePhoneticName: '',
    agxStoreEnglishName: '',
    agxUrl: '',
    agxBrandName: '',
    agxBusinessDate: '',
    agxRegularHoliday: '',
    agxBusinesssHours: '',
    agxStoreAddressCopyFlag1: false,
    agxStoreAddressCopyFlag2: false,
    agxStorePostalCode: '',
    agxStorePrefecture: '',
    agxStoreAddress1: '',
    agxStoreAddress2: '',
    agxStorePhoneticPrefecture: '',
    agxStorePhoneticAddress1: '',
    agxStorePhoneticAddress2: '',
    agxStorePhoneNumber: '',
    agxStoreFaxNumber: '',
    agxBankNo: '',
    agxBankName: '',
    agxBankType: *********,
    agxBranchNo: '',
    agxBranchName: '',
    agxBranchType: *********,
    agxBankPhoneticName: '',
    agxBranchPhoneticName: '',
    agxAccountType: *********,
    agxAccountNo: '',
    agxAccountHolder: '',
    agxContactName: '',
    agxContactEmail: '',
    agxContactPhoneticName: '',
    agxContactPhoneNumber: '',
    agxCapital: 0,
    agxNumberOfEmployees: '',
    agxFoundingDate: null,
    agxMonthlySales: 0,
    agxDoorToDoorSales: false,
    agxTelemarketingSales: false,
    agxPyramidScheme: false,
    agxBusinessOpportunityRelatedSales: false,
    agxSpecifiedContinuousServices: false,
    agxCardInformationRetentionStatus: *********,
    agxNoRetainingCardInfoDate: null,
    agxPcidssStatus: *********,
    agxPcidssExpectedComplianceDate: null,
    agxThreeDSecureStatus: *********,
    agxThreeDSecureDate: null,
    agxSecurityCodeCheckStatus: *********,
    agxSecurityCodeCheckDate: null,
    agxIllegalDeliveryDestinationStatus: *********,
    agxIllegalDeliveryDestinationDate: null,
    agxBehaviorAnalysisStatus: *********,
    agxBehaviorAnalysisDate: null,
    agxOtherMeasuresStatus: *********,
    agxOtherMeasuresDate: null,
    agxOtherMeasuresDescription: '',
    agxNumberOfTerminal: 1,
    agxColorOfTerminal: *********,
    agxSettlementCard: true,
    agxSettlementJcb: true,
    agxSettlementTraffic: false,
    agxSettlementNanaco: false,
    agxSettlementWaon: false,
    agxSettlementEdy: false,
    agxSettlementAid: false,
    agxSettlementQuicpay: false,
    agxSettlementQrCode: false,
    memberType: null,
    agxSettlementPackage1: false,
    agxSettlementPackage2: false,
    agxApplicationStatus: null
};

export const dataAreaSettingInit: DataAreaSettingType = {
  agxAreas: [],
  agxSubAreas: [],
  agxSubAreaModal: [],
  loading: true,
  error: false
}

// Transaction codes for payment processing
export const TransactionCode = {
    売上: '0',
    取消: '1',
    返品: '2',
    处理未了: '3'
} as const;

// Transaction types for different payment methods
export const TransactionType = {
    クレジットカード: '2',
    iD: '4',
    QUICPay: '5',
    交通系電子マネー: '6',
    銀聯: '7',
    WAON: '8',
    Edy: '9',
    nanaco: 'A',
    QRコード: 'B'
} as const;

// Transaction categories for credit card payments
export const TransactioCategory = {
    クレジットカード一括: '10',
    クレジットカードボーナス: '2x',
    クレジットカード分割: '6x',
    クレジットカードリボ: '80',
} as const;

// Map for transaction code payment data
export const mapTransactionCodePaymentData = new Map([
    [0, "売上"],
    [1, "取消"],
    [2, "处理未了"]
]);

// Map for credit card transaction types
export const mapTransactionTypeCredit = new Map([
    ['10', "クレジットカード一括"],

    ['20', "クレジットカードボーナス"],
    ['21', "クレジットカードボーナス"],
    ['22', "クレジットカードボーナス"],
    ['23', "クレジットカードボーナス"],
    ['24', "クレジットカードボーナス"],
    ['25', "クレジットカードボーナス"],
    ['26', "クレジットカードボーナス"],
    ['27', "クレジットカードボーナス"],
    ['28', "クレジットカードボーナス"],
    ['29', "クレジットカードボーナス"],

    ['60', "クレジットカード分割"],
    ['61', "クレジットカード分割"],
    ['62', "クレジットカード分割"],
    ['63', "クレジットカード分割"],
    ['64', "クレジットカード分割"],
    ['65', "クレジットカード分割"],
    ['66', "クレジットカード分割"],
    ['67', "クレジットカード分割"],
    ['68', "クレジットカード分割"],
    ['69', "クレジットカード分割"],

    ['80', "クレジットカードリボ"]
]);

// Map for other transaction types
export const mapTransactionTypeOther = new Map([
    ['2', "デビット"],
    ['4', "iD"],
    ['5', "QUICPay"],
    ['6', "交通系電子マネー"],
    ['7', "銀聯"],
    ['8', "WAON"],
    ['9', "Edy"],
    ['A', "nanaco"],
    ['B', "QRコード"]
]);
export const mapTransactionImage = new Map<number, string[]>([
  [*********, []], // クレジットカード一括
  [*********, []], // クレジットカードボーナス
  [*********, []], // クレジットカード分割
  [*********, []], // クレジットカードリボ
  [*********, [group1300]], // 銀聯
  [*********, [
    group1278, // threedot1dot1
    group1280, // threedot1dot2
    group1282, // threedot1dot3
    group1284, // threedot1dot4
    group1286, // threedot1dot5
    group1288, // threedot2dot1
    group1292, // threedot2dot2
    group1294, // threedot2dot3
    group1296, // threedot2dot4
  ]],
  [*********, [group1299]], // WAON
  [*********, [group1308]], // nanaco
  [*********, [group1310]], // Edy
  [*********, [group1301]], // iD
  [*********, [group1261]], // PayPay
  [*********, [group1268]], // メルペイ
  [*********, [group1265]], // auPAY
  [*********, []], // LinePay (chưa có icon)
  [*********, [group1263]], // d払い
  [*********, [group1272]], // アリペイ
  [*********, [group1270]], // WeChatPay
  [*********, [group1276]], // BankPay
  [*********, []], // クレジットカード一括(JCB)
  [*********, []], // クレジットカード2回払(JCB)
  [*********, []], // クレジットカードボーナス(JCB)
  [*********, []], // クレジットカード分割(JCB)
  [*********, []], // クレジットカードリボ(JCB)
  [*********, []], // JCBプレモ
  [*********, [group1312]], // QUICPay
]);
